# GoProxy设置功能使用指南

## 概述

GoProxy设置功能允许用户启用或禁用GoProxyService，这是一个多线程下载加速服务，可以提高视频播放的加载速度和稳定性。

## 功能特性

- ✅ **开关控制**: 用户可以通过设置页面的开关启用或禁用GoProxy
- ✅ **端口配置**: 支持自定义GoProxy服务的端口号
- ✅ **持久化存储**: 设置会自动保存，应用重启后保持
- ✅ **智能集成**: 视频播放器会根据设置自动使用或不使用GoProxy
- ✅ **错误处理**: 即使GoProxy启动失败，也不会影响正常播放

## 使用方法

### 1. 访问设置

1. 打开应用主页
2. 点击右上角的设置图标
3. 在设置页面找到"开发者选项"分组
4. 找到"GoProxy 加速"选项

### 2. 启用/禁用GoProxy

- **开关控制**: 点击右侧的开关即可启用或禁用GoProxy
- **状态显示**: 副标题会显示当前状态（"已禁用" 或 "已启用 (端口: XXXX)"）

### 3. 高级设置

点击"GoProxy 加速"选项可以打开高级设置对话框：

- **启用开关**: 控制GoProxy的启用状态
- **端口设置**: 自定义GoProxy服务的端口号（1-65535）
- **重置按钮**: 将所有设置重置为默认值

### 4. 默认设置

- **启用状态**: 默认禁用
- **端口号**: 37150

## 技术实现

### 架构组件

1. **GoProxySettingsService**: 核心设置服务
   - 管理启用/禁用状态
   - 管理端口配置
   - 提供持久化存储

2. **设置页面集成**: 
   - 开关控件
   - 高级设置对话框
   - 实时状态更新

3. **视频播放器集成**:
   - 根据设置自动启动/停止GoProxy
   - 智能URL代理
   - 错误处理和降级

### 代码示例

```dart
// 获取设置服务
final goProxySettings = GetIt.instance<GoProxySettingsService>();

// 检查是否启用
if (goProxySettings.isEnabled) {
  // 启动GoProxy
  await GoProxyService.startProxy(port: goProxySettings.port);
  
  // 获取代理URL
  final proxyUrl = await GoProxyService.getProxyUrl(originalUrl);
}
```

## 注意事项

1. **端口冲突**: 确保选择的端口没有被其他应用占用
2. **网络权限**: GoProxy需要网络权限才能正常工作
3. **重启生效**: 修改端口后建议重启应用
4. **错误处理**: 即使GoProxy启动失败，视频播放仍会使用原始URL

## 故障排除

### GoProxy启动失败

1. 检查端口是否被占用
2. 确认网络权限
3. 尝试使用不同的端口
4. 重启应用

### 视频播放问题

1. 尝试禁用GoProxy
2. 检查网络连接
3. 查看调试日志

## 开发者信息

- **服务类**: `GoProxySettingsService`
- **配置文件**: 使用SharedPreferences存储
- **测试文件**: `test/go_proxy_settings_service_test.dart`
- **依赖注入**: 通过ServiceLocator管理

## 更新日志

### v1.0.0
- 初始版本
- 基本的启用/禁用功能
- 端口配置
- 设置页面集成
- 视频播放器集成
