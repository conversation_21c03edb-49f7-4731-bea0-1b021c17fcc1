import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// GoProxy设置服务
/// 管理GoProxyService的启用/禁用状态
class GoProxySettingsService extends ChangeNotifier {
  static const String _enabledKey = 'go_proxy_enabled';
  static const String _portKey = 'go_proxy_port';
  
  bool _isEnabled = false;
  int _port = 37150; // 默认端口
  bool _isInitialized = false;
  
  /// 获取GoProxy是否启用
  bool get isEnabled => _isEnabled;
  
  /// 获取GoProxy端口
  int get port => _port;
  
  /// 获取是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 单例模式
  static final GoProxySettingsService _instance = GoProxySettingsService._internal();
  factory GoProxySettingsService() => _instance;
  GoProxySettingsService._internal();
  
  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _loadSettings();
      _isInitialized = true;
    } catch (e) {
      // 如果加载失败，使用默认值
      _isEnabled = false;
      _port = 37150;
      _isInitialized = true;
    }
  }
  
  /// 从SharedPreferences加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _isEnabled = prefs.getBool(_enabledKey) ?? false;
    _port = prefs.getInt(_portKey) ?? 37150;
  }
  
  /// 设置GoProxy启用状态
  Future<void> setEnabled(bool enabled) async {
    if (_isEnabled != enabled) {
      _isEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_enabledKey, enabled);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save GoProxy enabled setting: $e');
      }
    }
  }
  
  /// 设置GoProxy端口
  Future<void> setPort(int port) async {
    if (_port != port && port > 0 && port <= 65535) {
      _port = port;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_portKey, port);
      } catch (e) {
        // 保存失败不影响功能，但可以记录日志
        debugPrint('Failed to save GoProxy port setting: $e');
      }
    }
  }
  
  /// 重置为默认设置
  Future<void> resetToDefaults() async {
    _isEnabled = false;
    _port = 37150;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enabledKey, false);
      await prefs.setInt(_portKey, 37150);
    } catch (e) {
      debugPrint('Failed to reset GoProxy settings: $e');
    }
  }
  
  /// 获取设置摘要信息
  String get settingsSummary {
    if (!_isEnabled) {
      return '已禁用';
    }
    return '已启用 (端口: $_port)';
  }

  /// 重置服务状态（仅用于测试）
  void resetForTesting() {
    _isEnabled = false;
    _port = 37150;
    _isInitialized = false;
  }
}
