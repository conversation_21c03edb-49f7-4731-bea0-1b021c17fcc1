import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:my_115_app/services/token_manager.dart';
import 'package:get_it/get_it.dart';

import '../models/file_item.dart';
import '../services/api_service.dart';
import '../services/go_proxy_settings_service.dart';
import '../utils/go_proxy_service.dart';

class MediaKitVideoPlayerPage extends StatefulWidget {
  final List<FileItem> files;
  final int initialIndex;

  const MediaKitVideoPlayerPage({
    super.key,
    required this.files,
    required this.initialIndex,
  });

  @override
  State<MediaKitVideoPlayerPage> createState() => _MediaKitVideoPlayerPageState();
}

class _MediaKitVideoPlayerPageState extends State<MediaKitVideoPlayerPage> {

  late final Player player = Player(configuration: PlayerConfiguration(
    bufferSize: 256 * 1024 * 1024,
  ));
  late final NativePlayer nativePlayer = player.platform as NativePlayer;


  late final VideoController controller = VideoController(player);
  final ApiService _apiService = ApiService();
  final GoProxySettingsService _goProxySettings = GetIt.instance<GoProxySettingsService>();

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  late List<FileItem> _videoFiles;
  late int _adjustedInitialIndex;
  bool _showPlaylist = false;
  int _currentPlayingIndex = 0;

  @override
  void initState() {
    super.initState();

    nativePlayer.setProperty('prefetch-playlist', 'yes');
    nativePlayer.setProperty('cache', 'yes');

    // 根据设置启动 Go 代理服务器
    _initializeGoProxy();

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    // 过滤出视频文件
    _videoFiles = widget.files.where((file) => file.isVideo && file.pickCode != null).toList();
    
    if (_videoFiles.isEmpty) {
      _hasError = true;
      _errorMessage = '没有找到可播放的视频文件';
      setState(() {
        _isLoading = false;
      });
      return;
    }

    // 调整初始索引到视频文件列表中的位置
    _adjustedInitialIndex = _findAdjustedIndex();
    _currentPlayingIndex = _adjustedInitialIndex;

    // 批量获取所有视频的播放地址
    _initializeVideoPlaylist();

    // 监听播放列表变化
    _setupPlaylistListener();
  }

  /// 初始化GoProxy服务
  Future<void> _initializeGoProxy() async {
    if (_goProxySettings.isEnabled) {
      try {
        await GoProxyService.startProxy(port: _goProxySettings.port);
      } catch (e) {
        // 启动失败不影响播放，只是不使用代理
        debugPrint('GoProxy启动失败: $e');
      }
    }
  }

  /// 设置播放列表监听器
  void _setupPlaylistListener() {
    player.stream.playlist.listen((playlist) {
      if (playlist.index != _currentPlayingIndex) {
        setState(() {
          _currentPlayingIndex = playlist.index;
        });
      }
    });
  }

  /// 找到初始视频在过滤后的视频列表中的位置
  int _findAdjustedIndex() {
    if (widget.initialIndex < 0 || widget.initialIndex >= widget.files.length) {
      return 0;
    }
    
    final originalFile = widget.files[widget.initialIndex];
    
    // 如果原始文件是视频，在视频列表中找到它的位置
    if (originalFile.isVideo && originalFile.pickCode != null) {
      for (int i = 0; i < _videoFiles.length; i++) {
        if (_videoFiles[i].id == originalFile.id) {
          return i;
        }
      }
    }
    
    // 如果原始文件不是视频或没找到，返回0
    return 0;
  }

  /// 初始化视频播放列表
  Future<void> _initializeVideoPlaylist() async {
    try {
      // 提取所有视频的pick_code
      final pickCodes = _videoFiles
          .map((file) => file.pickCode!)
          .toList();

      // 批量获取播放地址
      final playUrls = await _apiService.getBatchPlayUrls(pickCodes);
      
      // 创建媒体列表
      final mediaList = <Media>[];
      for (final file in _videoFiles) {
        final playUrl = playUrls[file.pickCode];
        if (playUrl != null) {
          String finalUrl = playUrl;

          // 如果启用了GoProxy，尝试获取代理URL
          if (_goProxySettings.isEnabled) {
            try {
              final proxyUrl = await GoProxyService.getProxyUrl(playUrl);
              if (proxyUrl.isNotEmpty) {
                finalUrl = proxyUrl;
              }
            } catch (e) {
              // 获取代理URL失败，使用原始URL
              debugPrint('获取代理URL失败: $e');
            }
          }

          mediaList.add(Media(
            finalUrl,
            httpHeaders: {'User-Agent': TokenManager.userAgent},
          ));
        } else {
          // 如果某个视频获取地址失败，用占位符
          mediaList.add(Media('error://failed_to_load_${file.pickCode}'));
        }
      }

      if (mediaList.isEmpty) {
        throw Exception('所有视频都无法获取播放地址');
      }

      // 创建播放列表并开始播放
      final playlist = Playlist(mediaList, index: _adjustedInitialIndex);
      await player.open(playlist, play: true);

      setState(() {
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = '视频加载失败: ${e.toString()}';
      });
    }
  }

  @override
  void dispose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    player.dispose();

    // 如果启用了GoProxy，停止服务
    if (_goProxySettings.isEnabled) {
      GoProxyService.stopProxy().catchError((e) {
        debugPrint('停止GoProxy失败: $e');
      });
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              '正在加载视频...',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('返回'),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        // 主视频播放器
        MaterialVideoControlsTheme(
          // ===================================================================
          // 正常模式 (Normal Mode)
          // ===================================================================
          normal: MaterialVideoControlsThemeData(
            // -------------------- 手势控制 (Gesture Control) --------------------
            visibleOnMount: true,
            // controlsHoverDuration: Duration(days: 1),
            volumeGesture: true,              // 开启音量手势（右侧屏幕垂直滑动）
            brightnessGesture: true,          // 开启亮度手势（左侧屏幕垂直滑动）
            seekGesture: true,                // 启用水平滑动手势
            speedUpOnLongPress: true,         // 开启长按倍速播放
            // horizontalGestureSensitivity: 100,// 降低水平手势灵敏度，减少与进度条冲突（值越小越灵敏）

            // -------------------- 按钮与布局 (Buttons & Layout) --------------------
            // primaryButtonBar: [],             // 禁用主屏幕中央的播放按钮（暂停时显示的大按钮）
            backdropColor: Colors.transparent, // 禁用控件显示时的背景遮罩，让视频内容始终清晰

            // 底部按钮栏的控件列表
            // 注意：进度条(MaterialPositionIndicator)不应放在这里，它由下面的 seekBar... 属性独立控制。
            bottomButtonBar: [
              SizedBox(width: 20.0),    // 左侧留白
              MaterialPositionIndicator(style: TextStyle(color: Colors.white, fontSize: 16.0)),
              Spacer(),                 // 伸缩空间，将右侧按钮推到最右边
              // 播放列表按钮
              IconButton(
                icon: Icon(Icons.playlist_play, color: Colors.white, size: 28.0),
                onPressed: () {
                  setState(() {
                    _showPlaylist = !_showPlaylist;
                  });
                },
              ),
              MaterialSkipPreviousButton(),   // 上一个视频
              MaterialPlayOrPauseButton(),    // 播放/暂停按钮
              MaterialSkipNextButton(),       // 下一个视频
              MaterialFullscreenButton(),     // 全屏按钮
            ],
            // 底部按钮栏的边距
            bottomButtonBarMargin: const EdgeInsets.only(left: 10.0, right: 10.0, bottom: 20.0),
            buttonBarButtonSize: 28.0,        // 按钮图标的大小
            buttonBarButtonColor: Colors.white,// 按钮图标的颜色

            // -------------------- 进度条样式与位置 (Seek Bar Style & Position) --------------------
            // 进度条边距。通过设置大的 bottom 值，可以手动将进度条从底部抬高到按钮栏的上方。
            // 这是一个常用技巧，但布局可能因屏幕尺寸而异。
            seekBarMargin: const EdgeInsets.only(left: 20.0, right: 20.0, bottom: 90.0),
            seekBarHeight: 4.0,               // 进度条的高度，稍微增加便于视觉识别
            // seekBarContainerHeight: 60.0,      // 进度条容器高度
            // seekBarThumbSize: 24.0,           // 进度条拖动圆点大小
            seekBarColor: Colors.white,       // 进度条背景（未播放部分）的颜色
            seekBarPositionColor: Colors.blue,// 进度条已播放部分的颜色
            seekBarBufferColor: Colors.white38,// 进度条已缓冲部分的颜色
            seekBarThumbColor: Colors.blue,   // 进度条拖动圆点的颜色
            // 添加更多触摸友好的配置
            seekOnDoubleTap: true,            // 启用双击快进/快退
            seekBarAlignment: Alignment.center,
          ),

          // ===================================================================
          // 全屏模式 (Fullscreen Mode)
          // ===================================================================
          fullscreen: MaterialVideoControlsThemeData(
            // -------------------- 手势控制 (Gesture Control) --------------------
            volumeGesture: true,              // 全屏时同样开启音量手势
            brightnessGesture: true,          // 全屏时同样开启亮度手势
            seekGesture: true,                // 全屏时也启用水平滑动手势
            speedUpOnLongPress: true,         // 全屏时同样开启长按倍速播放
            // horizontalGestureSensitivity: 200, // 全屏模式下也降低水平手势灵敏度

            // -------------------- 按钮与布局 (Buttons & Layout) --------------------
            primaryButtonBar: [],
            backdropColor: Colors.transparent,

            // 为全屏模式设计更宽敞的按钮布局
            bottomButtonBar: [
              SizedBox(width: 20.0),    // 左侧留白
              MaterialPositionIndicator(style: TextStyle(color: Colors.white, fontSize: 16.0)),
              Spacer(),                 // 伸缩空间，将右侧按钮推到最右边
              // 播放列表按钮（全屏模式）
              IconButton(
                icon: Icon(Icons.playlist_play, color: Colors.white, size: 32.0),
                onPressed: () {
                  setState(() {
                    _showPlaylist = !_showPlaylist;
                  });
                },
              ),
              MaterialFullscreenButton(),     // 在全屏模式下，此按钮会自动变为"退出全屏"
              MaterialPlayOrPauseButton(),    // 播放/暂停按钮
              MaterialSkipPreviousButton(),   // 上一个视频
              MaterialSkipNextButton(),       // 下一个视频
            ],
            // 全屏模式下，底部边距可以适当调整
            bottomButtonBarMargin: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 20.0),
            buttonBarButtonSize: 32.0,        // 全屏时按钮可以稍大一些，便于点击
            buttonBarButtonColor: Colors.white,

            // -------------------- 进度条样式与位置 (Seek Bar Style & Position) --------------------
            // 全屏模式下，进度条的边距也需要相应调整
            seekBarMargin: EdgeInsets.only(left: 24.0, right: 24.0, bottom: 100.0),
            seekBarHeight: 4.0,               // 全屏模式下进度条高度，与正常模式保持一致
            // seekBarContainerHeight: 60.0,     // 全屏模式下也平衡触摸区域和手势冲突
            // seekBarThumbSize: 24.0,           // 全屏模式下也增加拖动圆点大小，与正常模式保持一致
            seekBarColor: Colors.white,
            seekBarPositionColor: Colors.blue,
            seekBarBufferColor: Colors.white38,
            seekBarThumbColor: Colors.blue,   // 全屏模式下拖动圆点的颜色
            seekBarAlignment: Alignment.center,
            // 全屏模式下也启用双击快进/快退
            seekOnDoubleTap: true,
          ),
          child: Center(
            child: Video(controller: controller),
          ),
        ),

        // 播放列表侧边栏
        if (_showPlaylist) _buildPlaylistDrawer(),
      ],
    );
  }

  /// 构建播放列表抽屉
  Widget _buildPlaylistDrawer() {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      width: 300,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          border: Border(
            left: BorderSide(color: Colors.white.withValues(alpha: 0.3), width: 1),
          ),
        ),
        child: Column(
          children: [
            // 播放列表标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.white.withValues(alpha: 0.3), width: 1),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.playlist_play, color: Colors.white, size: 24),
                  const SizedBox(width: 8),
                  const Text(
                    '播放列表',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _showPlaylist = false;
                      });
                    },
                  ),
                ],
              ),
            ),

            // 播放列表内容
            Expanded(
              child: ListView.builder(
                itemCount: _videoFiles.length,
                itemBuilder: (context, index) {
                  final file = _videoFiles[index];
                  final isCurrentPlaying = index == _currentPlayingIndex;

                  return Container(
                    decoration: BoxDecoration(
                      color: isCurrentPlaying
                          ? Colors.blue.withValues(alpha: 0.3)
                          : Colors.transparent,
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.white.withValues(alpha: 0.1),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 30,
                        decoration: BoxDecoration(
                          color: Colors.grey[800],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          isCurrentPlaying ? Icons.play_arrow : Icons.videocam,
                          color: isCurrentPlaying ? Colors.blue : Colors.white,
                          size: 20,
                        ),
                      ),
                      title: Text(
                        file.name,
                        style: TextStyle(
                          color: isCurrentPlaying ? Colors.blue : Colors.white,
                          fontSize: 14,
                          fontWeight: isCurrentPlaying ? FontWeight.bold : FontWeight.normal,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: file.formattedSize.isNotEmpty
                          ? Text(
                              file.formattedSize,
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            )
                          : null,
                      trailing: isCurrentPlaying
                          ? const Icon(Icons.volume_up, color: Colors.blue, size: 20)
                          : null,
                      onTap: () => _playVideoAtIndex(index),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 播放指定索引的视频
  void _playVideoAtIndex(int index) {
    if (index >= 0 && index < _videoFiles.length && index != _currentPlayingIndex) {
      player.jump(index);
      setState(() {
        _showPlaylist = false; // 选择后关闭播放列表
      });
    }
  }
}