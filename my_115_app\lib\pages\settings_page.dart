import 'package:flutter/material.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:get_it/get_it.dart';
import '../services/theme_service.dart';
import '../services/token_manager.dart';
import '../services/talker_service.dart';
import '../services/go_proxy_settings_service.dart';
import 'token_manager_page.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final ThemeService _themeService = ThemeService();
  final TalkerService _talkerService = GetIt.instance<TalkerService>();
  final GoProxySettingsService _goProxySettings = GetIt.instance<GoProxySettingsService>();

  @override
  void initState() {
    super.initState();
    _themeService.addListener(_onThemeChanged);
    _goProxySettings.addListener(_onGoProxySettingsChanged);
  }

  @override
  void dispose() {
    _themeService.removeListener(_onThemeChanged);
    _goProxySettings.removeListener(_onGoProxySettingsChanged);
    super.dispose();
  }

  void _onThemeChanged() {
    setState(() {});
  }

  void _onGoProxySettingsChanged() {
    setState(() {});
  }

  Future<void> _logout(BuildContext context) async {
    // 显示确认对话框
    final bool? shouldLogout = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认登出'),
          content: const Text('您确定要登出当前账户吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确认'),
            ),
          ],
        );
      },
    );

    if (shouldLogout == true && context.mounted) {
      final tokenManager = TokenManager();
      await tokenManager.clearTokens();
      
      if (context.mounted) {
        Navigator.of(context).pushReplacementNamed('/login');
      }
    }
  }

  void _showThemeSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择主题'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: AppThemeMode.values.map((mode) {
              return RadioListTile<AppThemeMode>(
                title: Text(_getThemeModeName(mode)),
                subtitle: Text(_getThemeModeDescription(mode)),
                value: mode,
                groupValue: _themeService.themeMode,
                onChanged: (AppThemeMode? value) {
                  if (value != null) {
                    _themeService.setThemeMode(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  void _showTokenManager(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TokenManagerPage(),
      ),
    );
  }

  String _getThemeModeName(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '明亮模式';
      case AppThemeMode.dark:
        return '暗黑模式';
      case AppThemeMode.system:
        return '跟随系统';
    }
  }

  String _getThemeModeDescription(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '始终使用明亮主题';
      case AppThemeMode.dark:
        return '始终使用暗黑主题';
      case AppThemeMode.system:
        return '跟随系统设置';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        children: [
          // 外观设置分组
          _buildSectionHeader('外观'),
          ListTile(
            leading: Icon(_themeService.themeModeIcon),
            title: const Text('主题模式'),
            subtitle: Text(_themeService.themeModeName),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showThemeSelector(context),
          ),
          const Divider(),

          // 开发者选项分组
          _buildSectionHeader('开发者选项'),
          ListTile(
            leading: const Icon(Icons.bug_report),
            title: const Text('调试日志'),
            subtitle: const Text('查看应用运行日志'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => TalkerScreen(
                  talker: _talkerService.talker,
                  theme: TalkerScreenTheme(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    textColor: Theme.of(context).colorScheme.onSurface,
                    cardColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                ),
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.speed),
            title: const Text('GoProxy 加速'),
            subtitle: Text(_goProxySettings.settingsSummary),
            trailing: Switch(
              value: _goProxySettings.isEnabled,
              onChanged: (bool value) {
                _goProxySettings.setEnabled(value);
              },
            ),
            onTap: () => _showGoProxySettings(context),
          ),
          const Divider(),

          // 账户设置分组
          _buildSectionHeader('账户'),
          ListTile(
            leading: const Icon(Icons.key),
            title: const Text('Token 管理'),
            subtitle: const Text('查看和编辑访问令牌'),
            trailing: const Icon(Icons.chevron_right),
            onTap: () => _showTokenManager(context),
          ),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('登出', style: TextStyle(color: Colors.red)),
            subtitle: const Text('退出当前账户'),
            onTap: () => _logout(context),
          ),
          const Divider(),

          // 应用信息分组
          _buildSectionHeader('关于'),
          const ListTile(
            leading: Icon(Icons.info_outline),
            title: Text('应用版本'),
            subtitle: Text('1.0.4'),
          ),
          const ListTile(
            leading: Icon(Icons.description),
            title: Text('应用名称'),
            subtitle: Text('115 文件管理'),
          ),
        ],
      ),
    );
  }

  void _showGoProxySettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('GoProxy 设置'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SwitchListTile(
                    title: const Text('启用 GoProxy'),
                    subtitle: const Text('启用多线程下载加速'),
                    value: _goProxySettings.isEnabled,
                    onChanged: (bool value) {
                      _goProxySettings.setEnabled(value);
                      setState(() {});
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: '端口号',
                      hintText: '请输入端口号 (1-65535)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    controller: TextEditingController(
                      text: _goProxySettings.port.toString(),
                    ),
                    onChanged: (String value) {
                      final port = int.tryParse(value);
                      if (port != null && port > 0 && port <= 65535) {
                        _goProxySettings.setPort(port);
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '注意：修改端口后需要重启应用才能生效',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('关闭'),
                ),
                TextButton(
                  onPressed: () {
                    _goProxySettings.resetToDefaults();
                    setState(() {});
                  },
                  child: const Text('重置'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
