import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_115_app/services/go_proxy_settings_service.dart';

void main() {
  group('GoProxySettingsService', () {
    late GoProxySettingsService service;

    setUp(() async {
      // 清除SharedPreferences
      SharedPreferences.setMockInitialValues({});
      service = GoProxySettingsService();
      // 重置服务状态
      service.resetForTesting();
    });

    test('初始化时应该使用默认值', () async {
      await service.initialize();
      
      expect(service.isEnabled, false);
      expect(service.port, 37150);
      expect(service.isInitialized, true);
      expect(service.settingsSummary, '已禁用');
    });

    test('应该能够设置启用状态', () async {
      await service.initialize();
      
      await service.setEnabled(true);
      
      expect(service.isEnabled, true);
      expect(service.settingsSummary, '已启用 (端口: 37150)');
    });

    test('应该能够设置端口', () async {
      await service.initialize();
      
      await service.setPort(8080);
      
      expect(service.port, 8080);
    });

    test('应该拒绝无效端口', () async {
      await service.initialize();
      
      await service.setPort(0);
      expect(service.port, 37150); // 应该保持默认值
      
      await service.setPort(70000);
      expect(service.port, 37150); // 应该保持默认值
      
      await service.setPort(-1);
      expect(service.port, 37150); // 应该保持默认值
    });

    test('应该能够重置为默认值', () async {
      await service.initialize();
      
      // 修改设置
      await service.setEnabled(true);
      await service.setPort(8080);
      
      // 重置
      await service.resetToDefaults();
      
      expect(service.isEnabled, false);
      expect(service.port, 37150);
    });

    test('设置应该持久化', () async {
      // 第一个实例
      await service.initialize();
      await service.setEnabled(true);
      await service.setPort(8080);

      // 模拟应用重启 - 重置状态然后重新初始化
      service.resetForTesting();
      await service.initialize();

      expect(service.isEnabled, true);
      expect(service.port, 8080);
    });

    test('应该正确生成设置摘要', () async {
      await service.initialize();
      
      expect(service.settingsSummary, '已禁用');
      
      await service.setEnabled(true);
      expect(service.settingsSummary, '已启用 (端口: 37150)');
      
      await service.setPort(8080);
      expect(service.settingsSummary, '已启用 (端口: 8080)');
    });
  });
}
